package iym.makos;

import iym.common.config.OracleJpaConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

@SpringBootApplication
@Import({OracleJpaConfig.class})
@Slf4j
public class MakosApplication {

    public static void main(String[] args) {
        try {
            SpringApplication.run(MakosApplication.class, args);
        } catch (Exception e) {
            log.error("Failed to start Makos Application!", e);
        }
    }
}
