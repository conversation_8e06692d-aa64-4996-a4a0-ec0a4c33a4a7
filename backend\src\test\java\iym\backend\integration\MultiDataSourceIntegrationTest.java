package iym.backend.integration;

import iym.common.config.OracleJpaConfig;
import iym.common.config.PostgreSQLJpaConfig;
import iym.common.model.entity.oracle.iym.MahkemeKararTalep;
import iym.common.model.entity.oracle.makos.MakosUser;
import iym.common.model.entity.postgresql.iym.Kullanici;
import iym.common.model.entity.postgresql.iym.MenuItem;
import iym.common.model.enums.MakosUserRoleType;
import iym.common.model.enums.UserStatusType;
import iym.common.model.enums.postgresql.enumKullaniciStatus;
import iym.common.repository.oracle.iym.MahkemeKararTalepRepo;
import iym.common.repository.oracle.makos.MakosUserRepo;
import iym.common.repository.postgresql.KullaniciRepository;
import iym.common.repository.postgresql.MenuItemRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test for Multi-DataSource configuration
 * Tests both Oracle and PostgreSQL databases working together
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.oracle.url=jdbc:h2:mem:oracle_test_db;MODE=Oracle;DB_CLOSE_DELAY=-1",
    "spring.datasource.oracle.driver-class-name=org.h2.Driver",
    "spring.datasource.oracle.username=sa",
    "spring.datasource.oracle.password=",
    "spring.datasource.postgresql.url=jdbc:h2:mem:postgresql_test_db;MODE=PostgreSQL;DB_CLOSE_DELAY=-1",
    "spring.datasource.postgresql.driver-class-name=org.h2.Driver",
    "spring.datasource.postgresql.username=sa",
    "spring.datasource.postgresql.password=",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class MultiDataSourceIntegrationTest {

    @Autowired
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;

    @Autowired
    private MakosUserRepo makosUserRepo;

    @Autowired
    private KullaniciRepository kullaniciRepository;

    @Autowired
    private MenuItemRepository menuItemRepository;

    @Test
    @Transactional("oracleTransactionManager")
    void testOracleDataSource_ShouldWorkCorrectly() {
        // Test MahkemeKararTalep (Oracle)
        MahkemeKararTalep karar = MahkemeKararTalep.builder()
                .evrakId(1001L)
                .kullaniciId(1L)
                .kayitTarihi(new Date())
                .durum("TEST")
                .mahkemeIlIlceKodu("0601")
                .build();

        MahkemeKararTalep savedKarar = mahkemeKararTalepRepo.save(karar);
        assertThat(savedKarar.getId()).isNotNull();
        assertThat(savedKarar.getDurum()).isEqualTo("TEST");

        // Test MakosUser (Oracle)
        MakosUser user = MakosUser.builder()
                .username("testuser")
                .password("password123")
                .status(UserStatusType.ACTIVE)
                .role(MakosUserRoleType.ROLE_KURUM_KULLANICI)
                .build();

        MakosUser savedUser = makosUserRepo.save(user);
        assertThat(savedUser.getId()).isNotNull();
        assertThat(savedUser.getUsername()).isEqualTo("testuser");
    }

    @Test
    @Transactional("postgresqlTransactionManager")
    void testPostgreSQLDataSource_ShouldWorkCorrectly() {
        // Test Kullanici (PostgreSQL)
        Kullanici kullanici = new Kullanici();
        kullanici.setKullaniciAdi("testuser");
        kullanici.setAd("Test");
        kullanici.setSoyad("User");
        kullanici.setEmail("<EMAIL>");
        kullanici.setParola("password123");
        kullanici.setStatus(enumKullaniciStatus.AKTIF);

        Kullanici savedKullanici = kullaniciRepository.save(kullanici);
        assertThat(savedKullanici.getId()).isNotNull();
        assertThat(savedKullanici.getKullaniciAdi()).isEqualTo("testuser");

        // Test MenuItem (PostgreSQL)
        MenuItem menuItem = MenuItem.builder()
                .label("Test Menu")
                .icon("test-icon")
                .routerLink("/test")
                .menuOrder(1)
                .build();

        MenuItem savedMenuItem = menuItemRepository.save(menuItem);
        assertThat(savedMenuItem.getId()).isNotNull();
        assertThat(savedMenuItem.getLabel()).isEqualTo("Test Menu");
    }

    @Test
    void testBothDataSources_ShouldWorkIndependently() {
        // Test Oracle operations
        testOracleOperations();
        
        // Test PostgreSQL operations
        testPostgreSQLOperations();
        
        // Verify both databases have data
        assertThat(mahkemeKararTalepRepo.count()).isGreaterThan(0);
        assertThat(kullaniciRepository.count()).isGreaterThan(0);
    }

    @Transactional("oracleTransactionManager")
    private void testOracleOperations() {
        MahkemeKararTalep karar = MahkemeKararTalep.builder()
                .evrakId(2001L)
                .kullaniciId(2L)
                .kayitTarihi(new Date())
                .durum("ORACLE_TEST")
                .mahkemeIlIlceKodu("0634")
                .build();

        mahkemeKararTalepRepo.save(karar);
    }

    @Transactional("postgresqlTransactionManager")
    private void testPostgreSQLOperations() {
        Kullanici kullanici = new Kullanici();
        kullanici.setKullaniciAdi("postgresqluser");
        kullanici.setAd("PostgreSQL");
        kullanici.setSoyad("User");
        kullanici.setEmail("<EMAIL>");
        kullanici.setParola("password123");
        kullanici.setStatus(enumKullaniciStatus.AKTIF);

        kullaniciRepository.save(kullanici);
    }

    @Test
    void testTransactionIsolation_ShouldMaintainSeparateTransactions() {
        // This test verifies that transactions are properly isolated between databases
        
        // Count initial records
        long initialOracleCount = mahkemeKararTalepRepo.count();
        long initialPostgreSQLCount = kullaniciRepository.count();

        // Add records to both databases
        testOracleOperations();
        testPostgreSQLOperations();

        // Verify counts increased independently
        assertThat(mahkemeKararTalepRepo.count()).isEqualTo(initialOracleCount + 1);
        assertThat(kullaniciRepository.count()).isEqualTo(initialPostgreSQLCount + 1);
    }

    @Test
    void testRepositoryQueries_ShouldWorkForBothDatabases() {
        // Test Oracle repository queries
        List<MahkemeKararTalep> oracleResults = mahkemeKararTalepRepo.findAll();
        assertThat(oracleResults).isNotNull();

        // Test PostgreSQL repository queries
        List<Kullanici> postgresqlResults = kullaniciRepository.findAll();
        assertThat(postgresqlResults).isNotNull();

        // Test specific queries
        Optional<MakosUser> makosUser = makosUserRepo.findByUsername("nonexistent");
        assertThat(makosUser).isEmpty();

        boolean userExists = kullaniciRepository.existsByKullaniciAdi("nonexistent");
        assertThat(userExists).isFalse();
    }
}