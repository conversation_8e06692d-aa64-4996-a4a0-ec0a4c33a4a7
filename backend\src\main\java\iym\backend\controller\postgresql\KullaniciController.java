package iym.backend.controller.postgresql;

import iym.common.model.entity.postgresql.iym.Kullanici;
import iym.common.service.postgresql.KullaniciService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * PostgreSQL Kullanici Controller
 * Handles user operations for PostgreSQL database
 */
@RestController("postgresqlKullaniciController")
@RequestMapping("/api/postgresql/kullanicilar")
@RequiredArgsConstructor
public class KullaniciController {

    private final KullaniciService kullaniciService;

    @PostMapping
    public <PERSON><PERSON><PERSON> create(@RequestBody Kullanici kullanici) {
        return kullaniciService.save(kullanici);
    }

    @GetMapping("/{id}")
    public Kullanici get(@PathVariable Long id) {
        return kullaniciService.findById(id)
                .orElseThrow(() -> new RuntimeException("<PERSON>llanıcı bulunamadı: " + id));
    }

    @GetMapping("/by-username/{kullaniciAdi}")
    public Kullanici getByUsername(@PathVariable String kullaniciAdi) {
        return kullaniciService.findByKullaniciAdi(kullaniciAdi)
                .orElseThrow(() -> new RuntimeException("Kullanıcı bulunamadı: " + kullaniciAdi));
    }

    @GetMapping("/exists/username/{kullaniciAdi}")
    public boolean existsByUsername(@PathVariable String kullaniciAdi) {
        return kullaniciService.existsByKullaniciAdi(kullaniciAdi);
    }

    @GetMapping("/exists/email/{email}")
    public boolean existsByEmail(@PathVariable String email) {
        return kullaniciService.existsByEmail(email);
    }

    @DeleteMapping("/{id}")
    public void delete(@PathVariable Long id) {
        kullaniciService.deleteById(id);
    }
}