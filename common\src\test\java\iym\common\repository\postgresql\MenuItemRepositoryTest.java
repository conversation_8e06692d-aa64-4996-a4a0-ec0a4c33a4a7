package iym.common.repository.postgresql;

import iym.common.config.PostgreSQLTestConfig;
import iym.common.config.TestApplication;
import iym.common.model.entity.postgresql.iym.MenuItem;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for PostgreSQL MenuItemRepository
 */
@DataJpaTest
@Import(PostgreSQLTestConfig.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver"
})
class MenuItemRepositoryTest {

    @Autowired
    private MenuItemRepository menuItemRepository;

    @Test
    void testFindByParentIsNullOrderByMenuOrder_ShouldReturnRootMenuItems() {
        // When
        List<MenuItem> rootMenuItems = menuItemRepository.findByParentIsNullOrderByMenuOrder();

        // Then
        assertThat(rootMenuItems).hasSize(3);
        assertThat(rootMenuItems.get(0).getLabel()).isEqualTo("Ana Sayfa");
        assertThat(rootMenuItems.get(1).getLabel()).isEqualTo("Kullanıcılar");
        assertThat(rootMenuItems.get(2).getLabel()).isEqualTo("Ayarlar");
        
        // Verify order
        assertThat(rootMenuItems.get(0).getMenuOrder()).isEqualTo(1);
        assertThat(rootMenuItems.get(1).getMenuOrder()).isEqualTo(2);
        assertThat(rootMenuItems.get(2).getMenuOrder()).isEqualTo(3);
    }

    @Test
    void testFindByParentOrderByMenuOrder_ShouldReturnChildMenuItems() {
        // Given
        Optional<MenuItem> parentMenu = menuItemRepository.findById(2L); // Kullanıcılar
        assertThat(parentMenu).isPresent();

        // When
        List<MenuItem> childMenuItems = menuItemRepository.findByParentOrderByMenuOrder(parentMenu.get());

        // Then
        assertThat(childMenuItems).hasSize(2);
        assertThat(childMenuItems.get(0).getLabel()).isEqualTo("Kullanıcı Listesi");
        assertThat(childMenuItems.get(1).getLabel()).isEqualTo("Yeni Kullanıcı");
        
        // Verify parent relationship
        assertThat(childMenuItems.get(0).getParent().getId()).isEqualTo(2L);
        assertThat(childMenuItems.get(1).getParent().getId()).isEqualTo(2L);
    }

    @Test
    void testFindAllWithYetkilerAndParent_ShouldReturnMenuItemsWithRelations() {
        // When
        List<MenuItem> menuItems = menuItemRepository.findAllWithYetkilerAndParent();

        // Then
        assertThat(menuItems).isNotEmpty();
        
        // Find a menu item with yetkiler
        Optional<MenuItem> kullanicilarMenu = menuItems.stream()
                .filter(m -> "Kullanıcılar".equals(m.getLabel()))
                .findFirst();
        
        assertThat(kullanicilarMenu).isPresent();
        assertThat(kullanicilarMenu.get().getMenuItemYetkiler()).isNotEmpty();
    }

    @Test
    void testSaveMenuItem_ShouldPersistMenuItem() {
        // Given
        MenuItem newMenuItem = MenuItem.builder()
                .label("Test Menu")
                .icon("test-icon")
                .routerLink("/test")
                .menuOrder(10)
                .build();

        // When
        MenuItem savedMenuItem = menuItemRepository.save(newMenuItem);

        // Then
        assertThat(savedMenuItem.getId()).isNotNull();
        assertThat(savedMenuItem.getLabel()).isEqualTo("Test Menu");
        assertThat(savedMenuItem.getIcon()).isEqualTo("test-icon");
        assertThat(savedMenuItem.getRouterLink()).isEqualTo("/test");
        assertThat(savedMenuItem.getMenuOrder()).isEqualTo(10);

        // Verify it can be found
        Optional<MenuItem> foundMenuItem = menuItemRepository.findById(savedMenuItem.getId());
        assertThat(foundMenuItem).isPresent();
        assertThat(foundMenuItem.get().getLabel()).isEqualTo("Test Menu");
    }

    @Test
    void testSaveMenuItemWithParent_ShouldPersistHierarchy() {
        // Given
        Optional<MenuItem> parentMenu = menuItemRepository.findById(1L); // Ana Sayfa
        assertThat(parentMenu).isPresent();

        MenuItem childMenuItem = MenuItem.builder()
                .label("Alt Menu")
                .icon("child-icon")
                .routerLink("/dashboard/child")
                .menuOrder(1)
                .parent(parentMenu.get())
                .build();

        // When
        MenuItem savedMenuItem = menuItemRepository.save(childMenuItem);

        // Then
        assertThat(savedMenuItem.getId()).isNotNull();
        assertThat(savedMenuItem.getParent()).isNotNull();
        assertThat(savedMenuItem.getParent().getId()).isEqualTo(1L);

        // Verify parent-child relationship
        List<MenuItem> childMenus = menuItemRepository.findByParentOrderByMenuOrder(parentMenu.get());
        assertThat(childMenus).hasSize(1);
        assertThat(childMenus.get(0).getLabel()).isEqualTo("Alt Menu");
    }
}