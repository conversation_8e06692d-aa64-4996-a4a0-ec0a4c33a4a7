package iym.common.repository.postgresql;

import iym.common.config.PostgreSQLTestConfig;
import iym.common.model.entity.postgresql.iym.Kullanici;
import iym.common.model.enums.postgresql.enumKullaniciStatus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for PostgreSQL KullaniciRepository
 */
@DataJpaTest
@Import(PostgreSQLTestConfig.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver"
})
class KullaniciRepositoryTest {

    @Autowired
    private KullaniciRepository kullaniciRepository;

    @Test
    void testFindByKullaniciAdi_ShouldReturnUser_WhenUserExists() {
        // Given
        String kullaniciAdi = "testuser1";

        // When
        Optional<Kullanici> result = kullaniciRepository.findByKullaniciAdi(kullaniciAdi);

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getKullaniciAdi()).isEqualTo(kullaniciAdi);
        assertThat(result.get().getAd()).isEqualTo("Test");
        assertThat(result.get().getSoyad()).isEqualTo("User1");
    }

    @Test
    void testFindByKullaniciAdi_ShouldReturnEmpty_WhenUserNotExists() {
        // Given
        String kullaniciAdi = "nonexistentuser";

        // When
        Optional<Kullanici> result = kullaniciRepository.findByKullaniciAdi(kullaniciAdi);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void testExistsByKullaniciAdi_ShouldReturnTrue_WhenUserExists() {
        // Given
        String kullaniciAdi = "testuser1";

        // When
        boolean exists = kullaniciRepository.existsByKullaniciAdi(kullaniciAdi);

        // Then
        assertThat(exists).isTrue();
    }

    @Test
    void testExistsByKullaniciAdi_ShouldReturnFalse_WhenUserNotExists() {
        // Given
        String kullaniciAdi = "nonexistentuser";

        // When
        boolean exists = kullaniciRepository.existsByKullaniciAdi(kullaniciAdi);

        // Then
        assertThat(exists).isFalse();
    }

    @Test
    void testExistsByEmail_ShouldReturnTrue_WhenEmailExists() {
        // Given
        String email = "<EMAIL>";

        // When
        boolean exists = kullaniciRepository.existsByEmail(email);

        // Then
        assertThat(exists).isTrue();
    }

    @Test
    void testSaveUser_ShouldPersistUser() {
        // Given
        Kullanici newUser = new Kullanici();
        newUser.setKullaniciAdi("newuser");
        newUser.setAd("New");
        newUser.setSoyad("User");
        newUser.setEmail("<EMAIL>");
        newUser.setParola("password123");
        newUser.setStatus(enumKullaniciStatus.AKTIF);

        // When
        Kullanici savedUser = kullaniciRepository.save(newUser);

        // Then
        assertThat(savedUser.getId()).isNotNull();
        assertThat(savedUser.getKullaniciAdi()).isEqualTo("newuser");
        assertThat(savedUser.getStatus()).isEqualTo(enumKullaniciStatus.AKTIF);

        // Verify it can be found
        Optional<Kullanici> foundUser = kullaniciRepository.findByKullaniciAdi("newuser");
        assertThat(foundUser).isPresent();
        assertThat(foundUser.get().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void testDeleteUser_ShouldSoftDelete() {
        // Given
        String kullaniciAdi = "testuser3";
        Optional<Kullanici> user = kullaniciRepository.findByKullaniciAdi(kullaniciAdi);
        assertThat(user).isPresent();

        // When
        kullaniciRepository.deleteById(user.get().getId());

        // Then
        Optional<Kullanici> deletedUser = kullaniciRepository.findByKullaniciAdi(kullaniciAdi);
        assertThat(deletedUser).isEmpty(); // Soft delete should make it invisible
    }
}