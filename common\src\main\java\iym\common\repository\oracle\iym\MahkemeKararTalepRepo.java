package iym.common.repository.oracle.iym;

import iym.common.model.entity.oracle.iym.MahkemeKararTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Oracle MahkemeKararTalep entity
 */
@Repository
public interface MahkemeKararTalepRepo extends JpaRepository<MahkemeKararTalep, Long> {

    List<MahkemeKararTalep> findByEvrakId(Long evrakId);
    

}