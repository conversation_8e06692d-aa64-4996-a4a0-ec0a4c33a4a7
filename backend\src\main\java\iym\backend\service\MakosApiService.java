package iym.backend.service;

import iym.makos.api.client.gen.api.HealthCheckControllerApi;
import iym.makos.api.client.gen.api.MahkemeKararControllerApi;
import iym.makos.api.client.gen.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.io.File;

/**
 * MAKOS API Service
 * Generated API client'ı kullanarak MAKOS işlemlerini gerçekleştirir
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MakosApiService {

    private final MahkemeKararControllerApi mahkemeKararControllerApi;
    private final HealthCheckControllerApi healthCheckControllerApi;

    /**
     * Health check işlemi
     *
     * @return String response
     */
    public HealthCheckResponse healthCheck() {
        try {
            log.info("Performing health check");
            HealthCheckResponse response = healthCheckControllerApi.healthCheck();
            log.info("Health check successful: {}", response);
            return response;
        } catch (RestClientException e) {
            log.error("Health check failed: {}", e.getMessage(), e);
            throw new RuntimeException("Health check failed", e);
        }
    }

    /**
     * Health check with basic authorization işlemi
     *
     * @return String response
     */
    public HealthCheckResponse healthCheckAuthorized() {
        try {
            log.info("Performing health check with basic authorization");
            HealthCheckResponse response = healthCheckControllerApi.healthCheckAuthorized();
            log.info("Health check with basic authorization successful: {}", response);
            return response;
        } catch (RestClientException e) {
            log.error("Health check with basic authorization failed: {}", e.getMessage(), e);
            throw new RuntimeException("Health check with basic authorization failed", e);
        }
    }

    /**
     * Mahkeme kodu güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return Object response
     */
    public Object mahkemeKoduGuncelle(File mahkemeKararDosyasi, IDMahkemeKoduGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating mahkeme kodu for request ID: {}", mahkemeKararDetay.getId());
            Object response = mahkemeKararControllerApi.mahkemeKoduGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Mahkeme kodu update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Mahkeme kodu update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Mahkeme kodu update failed", e);
        }
    }

    /**
     * ID yeni karar gönderme işlemi
     *
     * @param mahkemeKararDosyasiID Mahkeme karar dosyası
     * @param yeniKararRequest   Mahkeme karar detayları
     * @return Object response
     */
    public Object yeniKararID(File mahkemeKararDosyasiID, IDYeniKararRequest yeniKararRequest) {
        try {
            log.info("Sending ID karar for request ID: {}", yeniKararRequest.getId());
            Object response = mahkemeKararControllerApi.yeniKararID(mahkemeKararDosyasiID, yeniKararRequest);
            log.info("ID karar send successful for ID: {}", yeniKararRequest.getId());
            return response;
        } catch (RestClientException e) {
            log.error("ID karar send failed for ID {}: {}", yeniKararRequest.getId(), e.getMessage(), e);
            throw new RuntimeException("ID karar send failed", e);
        }
    }

    /**
     * IT karar gönderme işlemi
     *
     * @param mahkemeKararDosyasiIT Mahkeme karar dosyası
     * @param mahkemeKararDetayIT   Mahkeme karar detayları
     * @return Object response
     */
    public Object kararGonderIT(File mahkemeKararDosyasiIT, ITKararRequest mahkemeKararDetayIT) {
        try {
            log.info("Sending IT karar for request ID: {}", mahkemeKararDetayIT.getId());
            Object response = mahkemeKararControllerApi.kararGonderIT(mahkemeKararDosyasiIT, mahkemeKararDetayIT);
            log.info("IT karar send successful for ID: {}", mahkemeKararDetayIT.getId());
            return response;
        } catch (RestClientException e) {
            log.error("IT karar send failed for ID {}: {}", mahkemeKararDetayIT.getId(), e.getMessage(), e);
            throw new RuntimeException("IT karar send failed", e);
        }
    }

    /**
     * Hedef ad soyad güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return Object response
     */
    public Object hedefAdSoyadGuncelle(File mahkemeKararDosyasi, IDHedefAdSoyadGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating hedef ad soyad for request ID: {}", mahkemeKararDetay.getId());
            Object response = mahkemeKararControllerApi.hedefAdSoyadGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Hedef ad soyad update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Hedef ad soyad update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Hedef ad soyad update failed", e);
        }
    }

    /**
     * Canak no güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return Object response
     */
    public Object canakNoGuncelle(File mahkemeKararDosyasi, IDCanakGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating canak no for request ID: {}", mahkemeKararDetay.getId());
            Object response = mahkemeKararControllerApi.canakNoGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Canak no update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Canak no update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Canak no update failed", e);
        }
    }

    /**
     * Aidiyet bilgisi güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return Object response
     */
    public Object aidiyatBilgisiGuncelle(File mahkemeKararDosyasi, IDAidiyatBilgisiGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating aidiyet bilgisi for request ID: {}", mahkemeKararDetay.getId());
            Object response = mahkemeKararControllerApi.aidiyatBilgisiGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Aidiyet bilgisi update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Aidiyet bilgisi update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Aidiyet bilgisi update failed", e);
        }
    }
}
