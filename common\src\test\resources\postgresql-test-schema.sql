-- PostgreSQL Test Schema for H2 Database

-- Kullanicilar tablosu
CREATE TABLE kullanicilar (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    kullanici_adi VARCHAR(255) NOT NULL UNIQUE,
    tcno VARCHAR(11),
    ad VARCHAR(255),
    soyad VARCHAR(255),
    email VARCHAR(255),
    parola VARCHAR(255) NOT NULL,
    avatar_path VARCHAR(500),
    status VARCHAR(50) NOT NULL DEFAULT 'SIFRE_DEGISTIRMELI',
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_by <PERSON><PERSON><PERSON><PERSON>(255)
);

-- Kullanici Gruplar tablosu
CREATE TABLE kullanici_gruplar (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    ad VARCHAR(255) NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    deleted_by VARCHAR(255)
);

-- Yetkiler tablosu
CREATE TABLE yetkiler (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    ad VARCHAR(255),
    domain VARCHAR(255),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    deleted_by VARCHAR(255)
);

-- Menu Itemler tablosu
CREATE TABLE menu_itemler (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    label VARCHAR(255),
    icon VARCHAR(255),
    router_link VARCHAR(500),
    query_params VARCHAR(1000),
    menu_order INT DEFAULT 0,
    parent_id BIGINT,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    deleted_by VARCHAR(255),
    FOREIGN KEY (parent_id) REFERENCES menu_itemler(id)
);

-- Kullanici Kullanici Gruplar tablosu
CREATE TABLE kullanici_kullanici_gruplar (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    kullanici_id BIGINT NOT NULL,
    kullanici_grup_id BIGINT NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    deleted_by VARCHAR(255),
    FOREIGN KEY (kullanici_id) REFERENCES kullanicilar(id),
    FOREIGN KEY (kullanici_grup_id) REFERENCES kullanici_gruplar(id)
);

-- Kullanici Grup Yetkiler tablosu
CREATE TABLE kullanici_grup_yetkiler (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    kullanici_grup_id BIGINT NOT NULL,
    yetki_id BIGINT NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    deleted_by VARCHAR(255),
    FOREIGN KEY (kullanici_grup_id) REFERENCES kullanici_gruplar(id),
    FOREIGN KEY (yetki_id) REFERENCES yetkiler(id)
);

-- Menu Item Yetkiler tablosu
CREATE TABLE menu_item_yetkiler (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    menu_item_id BIGINT,
    yetki_id BIGINT,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    deleted_by VARCHAR(255),
    FOREIGN KEY (menu_item_id) REFERENCES menu_itemler(id),
    FOREIGN KEY (yetki_id) REFERENCES yetkiler(id)
);

-- Ulkeler tablosu
CREATE TABLE Ulkeler (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255),
    code VARCHAR(10),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    deleted_by VARCHAR(255)
);