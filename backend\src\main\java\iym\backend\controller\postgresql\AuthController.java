package iym.backend.controller.postgresql;

import iym.common.model.entity.postgresql.iym.Kullanici;
import iym.common.service.postgresql.KullaniciService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * PostgreSQL Authentication Controller
 * Handles authentication operations for PostgreSQL database
 */
@RestController
@RequestMapping("/api/postgresql/auth")
@RequiredArgsConstructor
public class AuthController {

    private final KullaniciService kullaniciService;
    private final AuthenticationManager authManager;

    @PostMapping("/login")
    public String login(@RequestBody LoginRequest request) {
        Authentication authentication = authManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword()));

        String username = authentication.getName();
        Kullanici kullanici = kullaniciService.findByKullaniciAdi(username)
                .orElseThrow(() -> new RuntimeException("<PERSON><PERSON>ı<PERSON>ı bulunamadı"));

        return "Login successful for user: " + kullanici.getKullaniciAdi();
    }

    /**
     * Login Request DTO
     */
    public static class LoginRequest {
        private String username;
        private String password;

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }
}