package iym.common.repository.postgresql;

import iym.common.model.entity.postgresql.iym.Kullanici;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for PostgreSQL Kullanici entity
 */
@Repository
public interface KullaniciRepository extends BaseRepository<Kullanici, Long> {
    
    /**
     * Find user by username
     */
    Optional<Kullanici> findByKullaniciAdi(String kullaniciAdi);
    
    /**
     * Check if user exists by username
     */
    boolean existsByKullaniciAdi(String kullaniciAdi);
    
    /**
     * Check if user exists by email
     */
    boolean existsByEmail(String email);
}