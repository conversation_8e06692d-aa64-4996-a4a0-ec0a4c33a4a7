package iym.common.service.postgresql;

import iym.common.model.entity.postgresql.iym.Kullanici;
import iym.common.repository.postgresql.KullaniciRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service for PostgreSQL Kullanici operations
 * Uses PostgreSQL transaction manager
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class KullaniciService {

    private final KullaniciRepository kullaniciRepository;

    /**
     * Find user by username
     */
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public Optional<Kullanici> findByKullaniciAdi(String kullaniciAdi) {
        log.debug("Finding user by kullaniciAdi: {}", kullaniciAdi);
        return kullaniciRepository.findByKullaniciAdi(kullaniciAdi);
    }

    /**
     * Save user
     */
    @Transactional("postgresqlTransactionManager")
    public Kullanici save(<PERSON><PERSON><PERSON> kullanici) {
        log.debug("Saving user: {}", kullanici.getKullaniciAdi());
        return kullaniciRepository.save(kullanici);
    }

    /**
     * Check if user exists by username
     */
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public boolean existsByKullaniciAdi(String kullaniciAdi) {
        return kullaniciRepository.existsByKullaniciAdi(kullaniciAdi);
    }

    /**
     * Check if user exists by email
     */
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public boolean existsByEmail(String email) {
        return kullaniciRepository.existsByEmail(email);
    }

    /**
     * Find user by ID
     */
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public Optional<Kullanici> findById(Long id) {
        return kullaniciRepository.findById(id);
    }

    /**
     * Delete user
     */
    @Transactional("postgresqlTransactionManager")
    public void deleteById(Long id) {
        log.debug("Deleting user with id: {}", id);
        kullaniciRepository.deleteById(id);
    }
}