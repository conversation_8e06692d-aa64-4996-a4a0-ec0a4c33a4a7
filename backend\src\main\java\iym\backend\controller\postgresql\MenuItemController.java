package iym.backend.controller.postgresql;

import iym.common.model.entity.postgresql.iym.MenuItem;
import iym.common.service.postgresql.MenuItemService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * PostgreSQL MenuItem Controller
 * Handles menu item operations for PostgreSQL database
 */
@RestController
@RequestMapping("/api/postgresql/menu-items")
@RequiredArgsConstructor
public class MenuItemController {

    private final MenuItemService menuItemService;

    @PostMapping
    public MenuItem create(@RequestBody MenuItem menuItem) {
        return menuItemService.save(menuItem);
    }

    @PutMapping("/{id}")
    public MenuItem update(@PathVariable Long id, @RequestBody MenuItem menuItem) {
        menuItem.setId(id);
        return menuItemService.save(menuItem);
    }

    @GetMapping("/{id}")
    public MenuItem get(@PathVariable Long id) {
        return menuItemService.findById(id)
                .orElseThrow(() -> new RuntimeException("MenuItem bulunamadı: " + id));
    }

    @GetMapping("/with-yetkiler")
    public List<MenuItem> getAllWithYetkiler() {
        return menuItemService.findAllWithYetkilerAndParent();
    }

    @GetMapping("/root")
    public List<MenuItem> getRootMenuItems() {
        return menuItemService.findRootMenuItems();
    }

    @GetMapping
    public List<MenuItem> getAll() {
        return menuItemService.findAll();
    }

    @DeleteMapping("/{id}")
    public void delete(@PathVariable Long id) {
        menuItemService.deleteById(id);
    }
}