package iym.backend;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

/**
 * Basic application context test
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.oracle.url=jdbc:h2:mem:oracle_test_db;MODE=Oracle;DB_CLOSE_DELAY=-1",
    "spring.datasource.oracle.driver-class-name=org.h2.Driver",
    "spring.datasource.oracle.username=sa",
    "spring.datasource.oracle.password=",
    "spring.datasource.postgresql.url=jdbc:h2:mem:postgresql_test_db;MODE=PostgreSQL;DB_CLOSE_DELAY=-1",
    "spring.datasource.postgresql.driver-class-name=org.h2.Driver",
    "spring.datasource.postgresql.username=sa",
    "spring.datasource.postgresql.password=",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class ApplicationTest {

    @Test
    void contextLoads() {
        // This test verifies that the Spring application context loads successfully
        // with multi-datasource configuration
    }
}