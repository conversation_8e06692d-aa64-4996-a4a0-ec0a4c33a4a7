-- Oracle Test Data

-- Test MAHKEME_<PERSON>ARAR_TALEP
INSERT INTO MAHKEME_KARAR_TALEP (EVRAK_ID, KULLANICI_ID, KAYIT_TARIHI, DURUM, HUKUK_BIRIM, KARAR_TIP, MAHKEME_ADI, MAHKEME_KARAR_NO, MAHKEME_ILI, ACIKLAMA) VALUES
(1001, 1, CURRENT_TIMESTAMP, 'BEKLEMEDE', 'Huku<PERSON> Birimi 1', '<PERSON>AR<PERSON>', 'Test Mahkemesi 1', 'K2024/001', '0601', 'Test karar talebi 1'),
(1002, 2, CURRENT_TIMESTAMP, 'ONAYLANDI', '<PERSON><PERSON><PERSON> 2', '<PERSON>AR<PERSON>', 'Test Mahkemesi 2', 'K2024/002', '0634', 'Test karar talebi 2'),
(1003, 1, CURRENT_TIMESTAMP, 'REDDEDILDI', '<PERSON><PERSON><PERSON> 1', '<PERSON><PERSON><PERSON><PERSON>', 'Test Mahkemesi 3', 'K2024/003', '0601', 'Test karar talebi 3');

-- Test MAKOS_USER
INSERT INTO MAKOS_USER (USERNAME, PASSWORD, STATUS, ROLE, KURUM) VALUES
('admin', 'admin123', 'ACTIVE', 'ADMIN', '{"kurumKodu": "001", "kurumAdi": "Test Kurumu 1"}'),
('user1', 'user123', 'ACTIVE', 'USER', '{"kurumKodu": "002", "kurumAdi": "Test Kurumu 2"}'),
('user2', 'user123', 'INACTIVE', 'USER', '{"kurumKodu": "003", "kurumAdi": "Test Kurumu 3"}');