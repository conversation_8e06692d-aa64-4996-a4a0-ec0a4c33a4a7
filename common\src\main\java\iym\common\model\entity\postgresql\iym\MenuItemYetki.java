package iym.common.model.entity.postgresql.iym;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "menu_item_yetkiler")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MenuItemYetki extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "menu_item_id")
    private MenuItem menuItem;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "yetki_id")
    private Yetki yetki;
}