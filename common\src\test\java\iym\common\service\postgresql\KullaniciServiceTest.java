package iym.common.service.postgresql;

import iym.common.model.entity.postgresql.iym.Kullanici;
import iym.common.model.enums.postgresql.enumKullaniciStatus;
import iym.common.repository.postgresql.KullaniciRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for PostgreSQL KullaniciService
 */
@ExtendWith(MockitoExtension.class)
class KullaniciServiceTest {

    @Mock
    private KullaniciRepository kullaniciRepository;

    @InjectMocks
    private KullaniciService kullaniciService;

    private <PERSON>llan<PERSON> testKullanici;

    @BeforeEach
    void setUp() {
        testKullanici = new Kullanici();
        testKullanici.setId(1L);
        testKullanici.setKullaniciAdi("testuser");
        testKullanici.setAd("Test");
        testKullanici.setSoyad("User");
        testKullanici.setEmail("<EMAIL>");
        testKullanici.setParola("password123");
        testKullanici.setStatus(enumKullaniciStatus.AKTIF);
    }

    @Test
    void testFindByKullaniciAdi_ShouldReturnUser_WhenUserExists() {
        // Given
        String kullaniciAdi = "testuser";
        when(kullaniciRepository.findByKullaniciAdi(kullaniciAdi))
                .thenReturn(Optional.of(testKullanici));

        // When
        Optional<Kullanici> result = kullaniciService.findByKullaniciAdi(kullaniciAdi);

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getKullaniciAdi()).isEqualTo(kullaniciAdi);
        assertThat(result.get().getAd()).isEqualTo("Test");
        
        verify(kullaniciRepository).findByKullaniciAdi(kullaniciAdi);
    }

    @Test
    void testFindByKullaniciAdi_ShouldReturnEmpty_WhenUserNotExists() {
        // Given
        String kullaniciAdi = "nonexistentuser";
        when(kullaniciRepository.findByKullaniciAdi(kullaniciAdi))
                .thenReturn(Optional.empty());

        // When
        Optional<Kullanici> result = kullaniciService.findByKullaniciAdi(kullaniciAdi);

        // Then
        assertThat(result).isEmpty();
        
        verify(kullaniciRepository).findByKullaniciAdi(kullaniciAdi);
    }

    @Test
    void testSave_ShouldReturnSavedUser() {
        // Given
        when(kullaniciRepository.save(any(Kullanici.class)))
                .thenReturn(testKullanici);

        // When
        Kullanici result = kullaniciService.save(testKullanici);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getKullaniciAdi()).isEqualTo("testuser");
        
        verify(kullaniciRepository).save(testKullanici);
    }

    @Test
    void testExistsByKullaniciAdi_ShouldReturnTrue_WhenUserExists() {
        // Given
        String kullaniciAdi = "testuser";
        when(kullaniciRepository.existsByKullaniciAdi(kullaniciAdi))
                .thenReturn(true);

        // When
        boolean result = kullaniciService.existsByKullaniciAdi(kullaniciAdi);

        // Then
        assertThat(result).isTrue();
        
        verify(kullaniciRepository).existsByKullaniciAdi(kullaniciAdi);
    }

    @Test
    void testExistsByKullaniciAdi_ShouldReturnFalse_WhenUserNotExists() {
        // Given
        String kullaniciAdi = "nonexistentuser";
        when(kullaniciRepository.existsByKullaniciAdi(kullaniciAdi))
                .thenReturn(false);

        // When
        boolean result = kullaniciService.existsByKullaniciAdi(kullaniciAdi);

        // Then
        assertThat(result).isFalse();
        
        verify(kullaniciRepository).existsByKullaniciAdi(kullaniciAdi);
    }

    @Test
    void testExistsByEmail_ShouldReturnTrue_WhenEmailExists() {
        // Given
        String email = "<EMAIL>";
        when(kullaniciRepository.existsByEmail(email))
                .thenReturn(true);

        // When
        boolean result = kullaniciService.existsByEmail(email);

        // Then
        assertThat(result).isTrue();
        
        verify(kullaniciRepository).existsByEmail(email);
    }

    @Test
    void testFindById_ShouldReturnUser_WhenUserExists() {
        // Given
        Long userId = 1L;
        when(kullaniciRepository.findById(userId))
                .thenReturn(Optional.of(testKullanici));

        // When
        Optional<Kullanici> result = kullaniciService.findById(userId);

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getId()).isEqualTo(userId);
        
        verify(kullaniciRepository).findById(userId);
    }

    @Test
    void testDeleteById_ShouldCallRepositoryDelete() {
        // Given
        Long userId = 1L;
        doNothing().when(kullaniciRepository).deleteById(userId);

        // When
        kullaniciService.deleteById(userId);

        // Then
        verify(kullaniciRepository).deleteById(userId);
    }
}