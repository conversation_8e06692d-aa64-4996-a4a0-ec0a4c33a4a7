package iym.common.service.postgresql;

import iym.common.repository.postgresql.BaseRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Base Service Implementation for PostgreSQL entities
 * Uses PostgreSQL transaction manager
 */
public abstract class BaseServiceImpl<TEntity, TDto, ID> implements BaseService<TDto, ID> {

    protected final BaseRepository<TEntity, ID> repository;
    protected final BaseMapper<TEntity, TDto> mapper;

    public BaseServiceImpl(BaseRepository<TEntity, ID> repository, BaseMapper<TEntity, TDto> mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    @Override
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public List<TDto> findAll() {
        return mapper.toDtoList(repository.findAll());
    }

    @Override
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public TDto findById(ID id) {
        TEntity entity = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Veri bulunamadı"));
        return mapper.toDto(entity);
    }

    @Override
    @Transactional("postgresqlTransactionManager")
    public TDto save(TDto dto) {
        TEntity entity = mapper.toEntity(dto);
        TEntity newEntity = repository.save(entity);
        return mapper.toDto(newEntity);
    }

    @Override
    @Transactional("postgresqlTransactionManager")
    public TDto update(ID id, TDto dto) {
        TEntity existing = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Güncellenecek veri bulunamadı"));

        mapper.updateEntityFromDto(dto, existing);
        return mapper.toDto(repository.save(existing));
    }

    @Override
    @Transactional("postgresqlTransactionManager")
    public void delete(ID id) {
        TEntity entity = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Silinecek veri bulunamadı"));
        repository.delete(entity);
    }

    /**
     * Base Mapper interface for PostgreSQL entities
     */
    public interface BaseMapper<TEntity, TDto> {
        TDto toDto(TEntity entity);
        TEntity toEntity(TDto dto);
        List<TDto> toDtoList(List<TEntity> entities);
        void updateEntityFromDto(TDto dto, TEntity entity);
    }
}