/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.AidiyatGuncellemeKararDetay;
import iym.makos.api.client.gen.model.EvrakDetay;
import iym.makos.api.client.gen.model.MahkemeKararBilgisi;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Mahkeme Karar Detaylari
 */
@JsonPropertyOrder({
  IDAidiyatBilgisiGuncellemeRequest.JSON_PROPERTY_ID,
  IDAidiyatBilgisiGuncellemeRequest.JSON_PROPERTY_KARAR_TURU,
  IDAidiyatBilgisiGuncellemeRequest.JSON_PROPERTY_EVRAK_DETAY,
  IDAidiyatBilgisiGuncellemeRequest.JSON_PROPERTY_MAHKEME_KARAR_BILGISI,
  IDAidiyatBilgisiGuncellemeRequest.JSON_PROPERTY_AIDIYAT_GUNCELLEME_KARAR_DETAY_LISTESI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class IDAidiyatBilgisiGuncellemeRequest {
  public static final String JSON_PROPERTY_ID = "id";
  private UUID id;

  /**
   * Gets or Sets kararTuru
   */
  public enum KararTuruEnum {
    _0("0"),
    
    _1("1"),
    
    _2("2"),
    
    _3("3"),
    
    _4("4"),
    
    _5("5"),
    
    _6("6"),
    
    _7("7"),
    
    _8("8");

    private String value;

    KararTuruEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static KararTuruEnum fromValue(String value) {
      for (KararTuruEnum b : KararTuruEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_KARAR_TURU = "kararTuru";
  private KararTuruEnum kararTuru;

  public static final String JSON_PROPERTY_EVRAK_DETAY = "evrakDetay";
  private EvrakDetay evrakDetay;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_BILGISI = "mahkemeKararBilgisi";
  private MahkemeKararBilgisi mahkemeKararBilgisi;

  public static final String JSON_PROPERTY_AIDIYAT_GUNCELLEME_KARAR_DETAY_LISTESI = "aidiyatGuncellemeKararDetayListesi";
  private List<AidiyatGuncellemeKararDetay> aidiyatGuncellemeKararDetayListesi = new ArrayList<>();

  public IDAidiyatBilgisiGuncellemeRequest() {
  }

  public IDAidiyatBilgisiGuncellemeRequest id(UUID id) {
    
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public UUID getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setId(UUID id) {
    this.id = id;
  }


  public IDAidiyatBilgisiGuncellemeRequest kararTuru(KararTuruEnum kararTuru) {
    
    this.kararTuru = kararTuru;
    return this;
  }

   /**
   * Get kararTuru
   * @return kararTuru
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_KARAR_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public KararTuruEnum getKararTuru() {
    return kararTuru;
  }


  @JsonProperty(JSON_PROPERTY_KARAR_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setKararTuru(KararTuruEnum kararTuru) {
    this.kararTuru = kararTuru;
  }


  public IDAidiyatBilgisiGuncellemeRequest evrakDetay(EvrakDetay evrakDetay) {
    
    this.evrakDetay = evrakDetay;
    return this;
  }

   /**
   * Get evrakDetay
   * @return evrakDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EVRAK_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public EvrakDetay getEvrakDetay() {
    return evrakDetay;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEvrakDetay(EvrakDetay evrakDetay) {
    this.evrakDetay = evrakDetay;
  }


  public IDAidiyatBilgisiGuncellemeRequest mahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
    return this;
  }

   /**
   * Get mahkemeKararBilgisi
   * @return mahkemeKararBilgisi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararBilgisi getMahkemeKararBilgisi() {
    return mahkemeKararBilgisi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
  }


  public IDAidiyatBilgisiGuncellemeRequest aidiyatGuncellemeKararDetayListesi(List<AidiyatGuncellemeKararDetay> aidiyatGuncellemeKararDetayListesi) {
    
    this.aidiyatGuncellemeKararDetayListesi = aidiyatGuncellemeKararDetayListesi;
    return this;
  }

  public IDAidiyatBilgisiGuncellemeRequest addAidiyatGuncellemeKararDetayListesiItem(AidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetayListesiItem) {
    if (this.aidiyatGuncellemeKararDetayListesi == null) {
      this.aidiyatGuncellemeKararDetayListesi = new ArrayList<>();
    }
    this.aidiyatGuncellemeKararDetayListesi.add(aidiyatGuncellemeKararDetayListesiItem);
    return this;
  }

   /**
   * Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek aidiyat bilgileri
   * @return aidiyatGuncellemeKararDetayListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_AIDIYAT_GUNCELLEME_KARAR_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<AidiyatGuncellemeKararDetay> getAidiyatGuncellemeKararDetayListesi() {
    return aidiyatGuncellemeKararDetayListesi;
  }


  @JsonProperty(JSON_PROPERTY_AIDIYAT_GUNCELLEME_KARAR_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAidiyatGuncellemeKararDetayListesi(List<AidiyatGuncellemeKararDetay> aidiyatGuncellemeKararDetayListesi) {
    this.aidiyatGuncellemeKararDetayListesi = aidiyatGuncellemeKararDetayListesi;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IDAidiyatBilgisiGuncellemeRequest idAidiyatBilgisiGuncellemeRequest = (IDAidiyatBilgisiGuncellemeRequest) o;
    return Objects.equals(this.id, idAidiyatBilgisiGuncellemeRequest.id) &&
        Objects.equals(this.kararTuru, idAidiyatBilgisiGuncellemeRequest.kararTuru) &&
        Objects.equals(this.evrakDetay, idAidiyatBilgisiGuncellemeRequest.evrakDetay) &&
        Objects.equals(this.mahkemeKararBilgisi, idAidiyatBilgisiGuncellemeRequest.mahkemeKararBilgisi) &&
        Objects.equals(this.aidiyatGuncellemeKararDetayListesi, idAidiyatBilgisiGuncellemeRequest.aidiyatGuncellemeKararDetayListesi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, kararTuru, evrakDetay, mahkemeKararBilgisi, aidiyatGuncellemeKararDetayListesi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IDAidiyatBilgisiGuncellemeRequest {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    kararTuru: ").append(toIndentedString(kararTuru)).append("\n");
    sb.append("    evrakDetay: ").append(toIndentedString(evrakDetay)).append("\n");
    sb.append("    mahkemeKararBilgisi: ").append(toIndentedString(mahkemeKararBilgisi)).append("\n");
    sb.append("    aidiyatGuncellemeKararDetayListesi: ").append(toIndentedString(aidiyatGuncellemeKararDetayListesi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

