-- Oracle Test Schema for H2 Database

-- <PERSON><PERSON><PERSON><PERSON>_KARAR_TALEP tablosu
CREATE TABLE MAHKEME_<PERSON>ARAR_TALEP (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    <PERSON>VRA<PERSON>_ID BIGINT NOT NULL,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_ID BIGINT NOT NULL,
    K<PERSON><PERSON><PERSON>_TARIHI TIMESTAMP NOT NULL,
    DURUM VARCHAR(20),
    HUKUK_BIRIM VARCHAR(50),
    <PERSON><PERSON><PERSON>_TIP VARCHAR(20),
    MAH_KARAR_BAS_TAR TIMESTAMP,
    MAH_KARAR_BITIS_TAR TIMESTAMP,
    MAHKEME_ADI VARCHAR(250),
    MAHKEME_KARAR_NO VARCHAR(50),
    <PERSON>HKEME_ILI VARCHAR(4) NOT NULL,
    ACIKLAMA VARCHAR(500),
    HA<PERSON>IM_SICIL_NO VARCHAR(20),
    SORUSTURMA_NO VARCHAR(50),
    GERCEK_MAH_ID BIGINT,
    MAHKEME_KODU VARCHAR(10)
);

-- MAKOS_USER tablosu
CREATE TABLE MAKOS_USER (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    USERNAME VARCHAR(100) NOT NULL UNIQUE,
    PASSWORD VARCHAR(100) NOT NULL,
    STATUS VARCHAR(20) NOT NULL,
    ROLE VARCHAR(20) NOT NULL,
    KURUM VARCHAR(500)
);