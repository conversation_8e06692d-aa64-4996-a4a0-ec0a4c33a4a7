package iym.backend;

import iym.common.config.OracleJpaConfig;
import iym.common.config.PostgreSQLJpaConfig;
import iym.spring.db.loader.DbLoader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

@SpringBootApplication
@Import({
    DbLoader.class,
    OracleJpaConfig.class,
    PostgreSQLJpaConfig.class
})
@Slf4j
public class Application {

    public static void main(String[] args) {
        try {
            SpringApplication.run(Application.class, args);
        } catch (Exception e) {
            log.error("Failed to start application", e);
        }

    }
}
