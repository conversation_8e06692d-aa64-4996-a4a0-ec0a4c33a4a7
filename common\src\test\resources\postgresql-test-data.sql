-- PostgreSQL Test Data

-- Test Kullanicilar
INSERT INTO kullanicilar (kullanici_adi, tcno, ad, soyad, email, parola, status, created_by) VALUES
('testuser1', '12345678901', 'Test', 'User1', '<EMAIL>', 'password123', 'AKTIF', 'system'),
('testuser2', '12345678902', 'Test', 'User2', '<EMAIL>', 'password123', 'AKTIF', 'system'),
('testuser3', '12345678903', 'Test', 'User3', '<EMAIL>', 'password123', 'PASIF', 'system');

-- Test Kullanici Gruplar
INSERT INTO kullanici_gruplar (ad, created_by) VALUES
('Admin Grubu', 'system'),
('Kullan<PERSON> Grubu', 'system'),
('Misafir Grubu', 'system');

-- Test Yetkiler
INSERT INTO yetkiler (ad, domain, created_by) VALUES
('USER_READ', 'USER', 'system'),
('USER_WRITE', 'USER', 'system'),
('USER_DELETE', 'USER', 'system'),
('MENU_READ', 'MENU', 'system'),
('MENU_WRITE', 'MENU', 'system');

-- Test Menu Items
INSERT INTO menu_itemler (label, icon, router_link, menu_order, created_by) VALUES
('Ana Sayfa', 'home', '/dashboard', 1, 'system'),
('Kullanıcılar', 'users', '/users', 2, 'system'),
('Ayarlar', 'settings', '/settings', 3, 'system');

-- Alt Menu Items
INSERT INTO menu_itemler (label, icon, router_link, menu_order, parent_id, created_by) VALUES
('Kullanıcı Listesi', 'list', '/users/list', 1, 2, 'system'),
('Yeni Kullanıcı', 'plus', '/users/new', 2, 2, 'system');

-- Test Kullanici Kullanici Gruplar
INSERT INTO kullanici_kullanici_gruplar (kullanici_id, kullanici_grup_id, created_by) VALUES
(1, 1, 'system'), -- testuser1 -> Admin Grubu
(2, 2, 'system'), -- testuser2 -> Kullanici Grubu
(3, 3, 'system'); -- testuser3 -> Misafir Grubu

-- Test Kullanici Grup Yetkiler
INSERT INTO kullanici_grup_yetkiler (kullanici_grup_id, yetki_id, created_by) VALUES
(1, 1, 'system'), -- Admin Grubu -> USER_READ
(1, 2, 'system'), -- Admin Grubu -> USER_WRITE
(1, 3, 'system'), -- Admin Grubu -> USER_DELETE
(1, 4, 'system'), -- Admin Grubu -> MENU_READ
(1, 5, 'system'), -- Admin Grubu -> MENU_WRITE
(2, 1, 'system'), -- Kullanici Grubu -> USER_READ
(2, 4, 'system'), -- Kullanici Grubu -> MENU_READ
(3, 4, 'system'); -- Misafir Grubu -> MENU_READ

-- Test Menu Item Yetkiler
INSERT INTO menu_item_yetkiler (menu_item_id, yetki_id, created_by) VALUES
(1, 4, 'system'), -- Ana Sayfa -> MENU_READ
(2, 4, 'system'), -- Kullanıcılar -> MENU_READ
(2, 5, 'system'), -- Kullanıcılar -> MENU_WRITE
(3, 4, 'system'), -- Ayarlar -> MENU_READ
(3, 5, 'system'), -- Ayarlar -> MENU_WRITE
(4, 4, 'system'), -- Kullanıcı Listesi -> MENU_READ
(5, 5, 'system'); -- Yeni Kullanıcı -> MENU_WRITE

-- Test Ulkeler
INSERT INTO Ulkeler (name, code, created_by) VALUES
('Türkiye', 'TR', 'system'),
('Amerika Birleşik Devletleri', 'US', 'system'),
('Almanya', 'DE', 'system');