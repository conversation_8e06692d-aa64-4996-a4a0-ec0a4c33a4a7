package iym.common.repository.postgresql;

import iym.common.model.entity.postgresql.iym.MenuItem;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for PostgreSQL MenuItem entity
 */
@Repository
public interface MenuItemRepository extends BaseRepository<MenuItem, Long> {
    
    /**
     * Find all menu items with their authorities and parent relationships
     */
    @Query("SELECT m FROM MenuItem m " +
           "LEFT JOIN FETCH m.menuItemYetkiler miy " +
           "LEFT JOIN FETCH miy.yetki " +
           "LEFT JOIN FETCH m.parent")
    List<MenuItem> findAllWithYetkilerAndParent();
    
    /**
     * Find root menu items (items without parent)
     */
    List<MenuItem> findByParentIsNullOrderByMenuOrder();
    
    /**
     * Find menu items by parent
     */
    List<MenuItem> findByParentOrderByMenuOrder(MenuItem parent);
}