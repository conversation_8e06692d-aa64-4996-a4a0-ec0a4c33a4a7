package iym.makos.config;

import org.springframework.context.annotation.Configuration;

/**
 * Filter configuration for MAKOS module
 * Configures audit logging filters for user operations
 *
 * TODO: Re-enable filters after migrating to new multi-datasource structure
 */
@Configuration
public class FilterConfig {

    // TODO: Re-implement filters with new multi-datasource structure

    /*
    private final DbMakosUserAuditLogService makosUserAuditLogService;
    private final DbMakosKararRequestLogService makosKararRequestLogService;

    public FilterConfig(DbMakosUserAuditLogService makosUserAuditLogService, DbMakosKararRequestLogService makosKararRequestLogService) {
        this.makosUserAuditLogService = makosUserAuditLogService;
        this.makosKararRequestLogService = makosKararRequestLogService;
    }

    @Bean
    public FilterRegistrationBean<MakosUserAuditLogFilter> makosUserAuditLogFilter() {
        FilterRegistrationBean<MakosUserAuditLogFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new MakosUserAuditLogFilter(makosUserAuditLogService));
        registrationBean.addUrlPatterns(
                "/auth/login",
                "/auth/register",
                "/auth/changePassword",
                "/user/getUsersForAdmin",
                "/user/activate",
                "/user/deactivate",
                "/user/add",
                "/user/update",
                "/user/delete");

        registrationBean.setOrder(1);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<MakosKararRequestLogFilter> makosKararRequestLogFilter() {
        FilterRegistrationBean<MakosKararRequestLogFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new MakosKararRequestLogFilter(makosKararRequestLogService));
        registrationBean.addUrlPatterns("/mahkemeKarar/*");
        registrationBean.setOrder(2);
        return registrationBean;
    }
    */
}
