package iym.common.repository.oracle.makos;

import iym.common.model.entity.oracle.makos.MakosUser;
import iym.common.model.enums.MakosUserRoleType;
import iym.common.model.enums.UserStatusType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Oracle MakosUser entity
 */
@Repository
public interface MakosUserRepo extends JpaRepository<MakosUser, Long> {

    Optional<MakosUser> findByUsername(String name);

    List<MakosUser> findByStatus(UserStatusType status);

    List<MakosUser> findByRoleOrderByUsernameAsc(MakosUserRoleType role);

    List<MakosUser> findAllByOrderByUsernameAsc();

}