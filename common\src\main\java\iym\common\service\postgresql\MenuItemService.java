package iym.common.service.postgresql;

import iym.common.model.entity.postgresql.iym.MenuItem;
import iym.common.repository.postgresql.MenuItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for PostgreSQL MenuItem operations
 * Uses PostgreSQL transaction manager
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MenuItemService {

    private final MenuItemRepository menuItemRepository;

    /**
     * Find all menu items with authorities and parent relationships
     */
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public List<MenuItem> findAllWithYetkilerAndParent() {
        log.debug("Finding all menu items with yetkiler and parent");
        return menuItemRepository.findAllWithYetkilerAndParent();
    }

    /**
     * Find root menu items
     */
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public List<MenuItem> findRootMenuItems() {
        log.debug("Finding root menu items");
        return menuItemRepository.findByParentIsNullOrderByMenuOrder();
    }

    /**
     * Find menu items by parent
     */
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public List<MenuItem> findByParent(MenuItem parent) {
        log.debug("Finding menu items by parent: {}", parent.getId());
        return menuItemRepository.findByParentOrderByMenuOrder(parent);
    }

    /**
     * Save menu item
     */
    @Transactional("postgresqlTransactionManager")
    public MenuItem save(MenuItem menuItem) {
        log.debug("Saving menu item: {}", menuItem.getLabel());
        return menuItemRepository.save(menuItem);
    }

    /**
     * Find menu item by ID
     */
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public Optional<MenuItem> findById(Long id) {
        return menuItemRepository.findById(id);
    }

    /**
     * Delete menu item
     */
    @Transactional("postgresqlTransactionManager")
    public void deleteById(Long id) {
        log.debug("Deleting menu item with id: {}", id);
        menuItemRepository.deleteById(id);
    }

    /**
     * Find all menu items
     */
    @Transactional(value = "postgresqlTransactionManager", readOnly = true)
    public List<MenuItem> findAll() {
        return menuItemRepository.findAll();
    }
}