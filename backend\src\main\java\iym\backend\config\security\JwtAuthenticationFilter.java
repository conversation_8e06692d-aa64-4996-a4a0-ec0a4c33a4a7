package iym.backend.config.security;

import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT Authentication Filter for IYM Backend
 * Validates JWT tokens and sets authentication in security context
 */
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    //@Autowired
    //private IymUserDetailsServiceImpl userDetailsService;

    @Autowired
    private JwtTokenUtil jwtTokenProvider;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) 
            throws ServletException, IOException {

        try {
            String jwtToken = getJwtToken(request);
            if (jwtToken != null && jwtTokenProvider.validateJwtToken(jwtToken)) {

                log.info("Checking jwt token for authentication");
                String username = jwtTokenProvider.getUserNameFromJwtToken(jwtToken);
                Claims claims = jwtTokenProvider.extractAllClaims(jwtToken);
                String actingUserName = claims.get(JwtTokenUtil.ACTING_USER_NAME_CLAIM_KEY, String.class);
                log.info("Jwt token details:{}", username + (actingUserName != null ? " - " + actingUserName : ""));

                //if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                //    IymUserDetailsImpl userDetails = userDetailsService.loadIymUserByUsername(username);
                //
                //    if (actingUserName != null && !actingUserName.isEmpty()){
                //        userDetails.setActingUserName(actingUserName);
                //        log.info("Acting user name set: {}", actingUserName);
                //    }
                //
                //    UsernamePasswordAuthenticationToken authentication =
                //            new UsernamePasswordAuthenticationToken(
                //                    userDetails,
                //                    null,
                //                    userDetails.getAuthorities());
                //    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                //
                //    log.info("Setting Authentication in security context:{}", userDetails);
                //    SecurityContextHolder.getContext().setAuthentication(authentication);
                //}
            }
        } catch (Exception ex) {
            log.error("Could not set user authentication in security context", ex);
        }

        filterChain.doFilter(request, response);
    }

    /**
     * Extract JWT token from Authorization header
     */
    private String getJwtToken(HttpServletRequest request) {
        String authHeader = request.getHeader(HttpHeaders.AUTHORIZATION);

        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        return null;
    }

    /**
     * Get username from request JWT token
     */
    private String getUsernameFromRequest(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader(HttpHeaders.AUTHORIZATION);

            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return null;
            }

            final String token = authHeader.split(" ")[1].trim();

            if (!jwtTokenProvider.validateJwtToken(token)) {
                return null;
            }

            return jwtTokenProvider.extractUsername(token);
        } catch (Exception ex) {
            log.error("Could not extract username from request", ex);
        }
        return null;
    }
}
