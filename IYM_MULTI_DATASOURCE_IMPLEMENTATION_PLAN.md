# IYM Multi-DataSource Implementation Plan

## 📋 Project Overview

**Project:** IYM Multi-Modular Application  
**Goal:** Integrate multi-datasource support (Oracle + PostgreSQL) with minimal impact to existing code  
**Status:** In Progress - Entity Folder Structure Reorganization Phase  
**Date:** 2025-07-07  

## 🎯 Current Task Status

### ✅ Completed Tasks
- [x] **Project Analysis and Preparation** - Java 17 compatibility confirmed, Spring Boot versions compatible
- [x] **Multi-DataSource Infrastructure Design** - Architecture planned with @Primary Oracle approach

### 🔄 Current Task
- [/] **Entity Folder Structure Reorganization** - Setting up oracle/postgresql package separation

### 📋 Pending Tasks
1. Dependency and Configuration Updates
2. JPA Configuration Classes Creation
3. Repository Layer Adaptation
4. Service Layer Transaction Management
5. springboot-angular-main Backend Integration
6. Unit Test Development
7. Integration Test and Validation

## 🏗️ Technical Architecture

### Database Configuration Strategy
```yaml
# Oracle (Primary - Existing)
spring.datasource.oracle.url=***********************************
spring.datasource.oracle.username=iym
spring.datasource.oracle.password=iym
spring.datasource.oracle.driver-class-name=oracle.jdbc.OracleDriver

# PostgreSQL (Secondary - New)
spring.datasource.postgresql.url=****************************************
spring.datasource.postgresql.username=demo_user
spring.datasource.postgresql.password=demo_password
spring.datasource.postgresql.driver-class-name=org.postgresql.Driver
```

### Entity Package Structure
```
common/src/main/java/iym/common/model/entity/
├── oracle/
│   ├── iym/          # MahkemeKararTalep, SucTipi, Iller, MahkemeSuclarTalep, etc.
│   └── makos/        # MakosUser, etc.
└── postgresql/
    └── iym/          # Kullanici, MenuItem, Yetki, KullaniciGrup, etc. (from springboot-angular-main)
```

### JPA Configuration Classes

#### OracleJpaConfig.java
```java
@Configuration
@EnableJpaRepositories(
    basePackages = "iym.common.repository.oracle",
    entityManagerFactoryRef = "oracleEntityManagerFactory",
    transactionManagerRef = "oracleTransactionManager"
)
public class OracleJpaConfig {
    @Primary
    @Bean
    @ConfigurationProperties("spring.datasource.oracle")
    public DataSource oracleDataSource() { ... }
    
    @Primary
    @Bean
    public LocalContainerEntityManagerFactoryBean oracleEntityManagerFactory() {
        // basePackages: "iym.common.model.entity.oracle"
    }
    
    @Primary
    @Bean
    public PlatformTransactionManager oracleTransactionManager() { ... }
}
```

#### PostgreSQLJpaConfig.java
```java
@Configuration
@EnableJpaRepositories(
    basePackages = "iym.common.repository.postgresql",
    entityManagerFactoryRef = "postgresqlEntityManagerFactory",
    transactionManagerRef = "postgresqlTransactionManager"
)
public class PostgreSQLJpaConfig {
    @Bean
    @ConfigurationProperties("spring.datasource.postgresql")
    public DataSource postgresqlDataSource() { ... }
    
    @Bean
    public LocalContainerEntityManagerFactoryBean postgresqlEntityManagerFactory() {
        // basePackages: "iym.common.model.entity.postgresql"
    }
    
    @Bean
    public PlatformTransactionManager postgresqlTransactionManager() { ... }
}
```

## 📁 File Migration Plan

### Entities to Move from springboot-angular-main/backend to common/model/entity/postgresql/iym/
- `Kullanici.java` → `iym.common.model.entity.postgresql.iym.Kullanici`
- `MenuItem.java` → `iym.common.model.entity.postgresql.iym.MenuItem`
- `Yetki.java` → `iym.common.model.entity.postgresql.iym.Yetki`
- `KullaniciGrup.java` → `iym.common.model.entity.postgresql.iym.KullaniciGrup`
- `MenuItemYetki.java` → `iym.common.model.entity.postgresql.iym.MenuItemYetki`
- `KullaniciKullaniciGrup.java` → `iym.common.model.entity.postgresql.iym.KullaniciKullaniciGrup`
- `BaseEntity.java` → `iym.common.model.entity.postgresql.iym.BaseEntity`

### Existing Oracle Entities to Reorganize
- Move from `iym.common.model.entity.iym.*` to `iym.common.model.entity.oracle.iym.*`
- Move from `iym.common.model.entity.makos.*` to `iym.common.model.entity.oracle.makos.*`

## 🔧 Repository Layer Strategy

### Oracle Repositories (Existing - Minimal Changes)
```java
// Package: iym.common.repository.oracle
@Repository
public interface MahkemeKararRepo extends JpaRepository<MahkemeKararTalep, Long> {
    // Existing methods remain unchanged
}
```

### PostgreSQL Repositories (New)
```java
// Package: iym.common.repository.postgresql
@Repository
public interface KullaniciRepository extends JpaRepository<Kullanici, Long> {
    Optional<Kullanici> findByKullaniciAdi(String kullaniciAdi);
}
```

## 🎯 Service Layer Transaction Management

### Oracle Services (Existing)
```java
@Service
@Transactional // Uses oracleTransactionManager (Primary)
public class MahkemeKararService {
    // Existing code remains unchanged
}
```

### PostgreSQL Services (New)
```java
@Service
@Transactional("postgresqlTransactionManager")
public class KullaniciService {
    // New service methods for PostgreSQL entities
}
```

## 🧪 Testing Strategy

### Unit Tests Structure
```
src/test/java/
├── oracle/
│   ├── OracleEntityTest.java
│   └── OracleRepositoryTest.java
└── postgresql/
    ├── PostgreSQLEntityTest.java
    └── PostgreSQLRepositoryTest.java
```

### Test Configuration
```java
@TestConfiguration
public class TestDataSourceConfig {
    @Bean
    @Primary
    public DataSource oracleTestDataSource() {
        // H2 in-memory database for Oracle tests
    }
    
    @Bean
    public DataSource postgresqlTestDataSource() {
        // H2 in-memory database for PostgreSQL tests
    }
}
```

## 📦 Required Dependencies (pom.xml updates)

### Main pom.xml additions:
```xml
<!-- PostgreSQL Driver -->
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    <version>42.7.3</version>
</dependency>

<!-- Flyway for PostgreSQL migrations -->
<dependency>
    <groupId>org.flywaydb</groupId>
    <artifactId>flyway-core</artifactId>
</dependency>
```

## ⚠️ Critical Success Factors

1. **Preserve Existing Functionality**: Oracle datasource remains @Primary
2. **Clean Package Separation**: Each datasource has distinct entity packages
3. **Proper Transaction Management**: Correct transaction manager assignments
4. **Comprehensive Testing**: Test coverage for both databases
5. **Minimal Code Impact**: Existing Oracle code should work without changes

## 🚀 Implementation Steps (Detailed)

### Step 1: Entity Reorganization (Current)
1. Create new package structure in common module
2. Move PostgreSQL entities from springboot-angular-main
3. Reorganize existing Oracle entities
4. Update import statements

### Step 2: Dependency Updates
1. Add PostgreSQL driver to main pom.xml
2. Add Flyway dependency
3. Update common module dependencies

### Step 3: JPA Configuration
1. Create OracleJpaConfig class
2. Create PostgreSQLJpaConfig class
3. Update application.properties with dual datasource config

### Step 4: Repository Adaptation
1. Create oracle repository package
2. Create postgresql repository package
3. Move existing repositories to oracle package
4. Create new repositories for postgresql entities

### Step 5: Service Layer Updates
1. Add transaction manager qualifiers where needed
2. Create new services for PostgreSQL entities
3. Test existing Oracle services still work

### Step 6: Integration and Testing
1. Write unit tests for both databases
2. Create integration tests
3. Validate all existing functionality works
4. Test new PostgreSQL functionality

## 📝 Notes for Future AI Agents

- **Current Phase**: Entity Folder Structure Reorganization
- **Next Critical Step**: Move entities and update package references
- **Key Principle**: Oracle remains @Primary to avoid breaking existing code
- **Test Strategy**: Each database should be testable independently
- **Package Convention**: `iym.common.model.entity.{database}.{domain}`

## 🔍 Validation Checklist

- [ ] All existing Oracle functionality works unchanged
- [ ] PostgreSQL entities are properly configured
- [ ] Both databases can be accessed simultaneously
- [ ] Transaction management works correctly for both databases
- [ ] All tests pass
- [ ] No circular dependencies
- [ ] Proper error handling for database connection issues

## 🔄 Current Implementation Status

### Active Task Details
**Current Task**: Entity Klasör Yapısı Reorganizasyonu
**Task ID**: 2QuLAgr5xjfzsRYi9sTm9A
**Status**: IN_PROGRESS

### Immediate Next Actions
1. Create directory structure: `common/src/main/java/iym/common/model/entity/postgresql/iym/`
2. Move PostgreSQL entities from `springboot-angular-main/backend/src/main/java/tr/gov/btk/`
3. Update package declarations and imports
4. Reorganize existing Oracle entities to `oracle/` subdirectories

### Files to Process in Current Task

#### PostgreSQL Entities (Source: springboot-angular-main/backend)
```
tr.gov.btk.kullanici.entity.Kullanici → iym.common.model.entity.postgresql.iym.Kullanici
tr.gov.btk.menuitem.entity.MenuItem → iym.common.model.entity.postgresql.iym.MenuItem
tr.gov.btk.yetki.entity.Yetki → iym.common.model.entity.postgresql.iym.Yetki
tr.gov.btk.kullaniciGrup.entity.KullaniciGrup → iym.common.model.entity.postgresql.iym.KullaniciGrup
tr.gov.btk.menuitem.entity.MenuItemYetki → iym.common.model.entity.postgresql.iym.MenuItemYetki
tr.gov.btk.kullanici.entity.KullaniciKullaniciGrup → iym.common.model.entity.postgresql.iym.KullaniciKullaniciGrup
tr.gov.btk.shared.entity.BaseEntity → iym.common.model.entity.postgresql.iym.BaseEntity
```

#### Oracle Entities (Current: common/src/main/java/iym/common/model/entity/)
```
iym.common.model.entity.iym.* → iym.common.model.entity.oracle.iym.*
iym.common.model.entity.makos.* → iym.common.model.entity.oracle.makos.*
```

## 🛠️ Technical Implementation Details

### Entity Migration Script Template
```bash
# For future AI agents - use this pattern for entity migration
# 1. Create target directory
mkdir -p common/src/main/java/iym/common/model/entity/postgresql/iym/

# 2. Copy entity files
cp springboot-angular-main/backend/src/main/java/tr/gov/btk/*/entity/*.java \
   common/src/main/java/iym/common/model/entity/postgresql/iym/

# 3. Update package declarations (use str-replace-editor)
# 4. Update import statements
# 5. Verify compilation
```

### Package Import Update Pattern
```java
// OLD (PostgreSQL entities)
package tr.gov.btk.kullanici.entity;

// NEW (PostgreSQL entities)
package iym.common.model.entity.postgresql.iym;

// OLD (Oracle entities)
package iym.common.model.entity.iym;

// NEW (Oracle entities)
package iym.common.model.entity.oracle.iym;
```

### Repository Package Structure
```
common/src/main/java/iym/common/repository/
├── oracle/
│   ├── iym/          # Oracle IYM repositories
│   └── makos/        # Oracle Makos repositories
└── postgresql/
    └── iym/          # PostgreSQL repositories
```

## 🎯 Success Metrics

### Phase Completion Criteria
- [ ] All entity files moved to correct packages
- [ ] No compilation errors
- [ ] All import statements updated
- [ ] Package declarations corrected
- [ ] Existing tests still pass (Oracle)
- [ ] New package structure validated

### Quality Gates
1. **Compilation Check**: `mvn clean compile` succeeds
2. **Test Validation**: Existing Oracle tests pass
3. **Package Verification**: No circular dependencies
4. **Import Cleanup**: No unused imports

## 🚨 Risk Mitigation

### Rollback Strategy
1. Keep backup of original files
2. Use git branches for each phase
3. Test compilation after each entity move
4. Validate existing functionality before proceeding

### Common Pitfalls to Avoid
- Don't move entities before creating target directories
- Update package declarations before fixing imports
- Test compilation frequently during migration
- Don't forget to update test files that reference moved entities

## 📞 Handoff Instructions for Next AI Agent

### Context Restoration
1. Read this plan document completely
2. Check current task status in task management system
3. Verify which entities have been moved (check file system)
4. Run `mvn clean compile` to check current state

### Continuation Points
- If entity reorganization incomplete: Continue moving entities per file list above
- If entity reorganization complete: Proceed to "Dependency ve Konfigürasyon Güncellemeleri" task
- If compilation errors: Fix package/import issues before proceeding

### Key Commands for Verification
```bash
# Check current directory structure
find common/src/main/java/iym/common/model/entity/ -type f -name "*.java"

# Verify compilation
mvn clean compile -pl common

# Check for package declaration issues
grep -r "package " common/src/main/java/iym/common/model/entity/
```

---
**Last Updated**: 2025-07-07
**Next Review**: After Entity Reorganization completion
**Document Version**: 1.1
**AI Agent Handoff Ready**: ✅
